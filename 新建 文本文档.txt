 // 获取所有板块数据用于搜索
            async function fetchAllSectors() {
                let index = 0;
                sectors = [];
                while (true) {
                    try {
                        const response = await fetch(`https://apphq.longhuvip.com/w1/api/index.php?Order=1&a=RealRankingInfo&st=60&apiv=w26&Type=1&c=ZhiShuRanking&PhoneOSNew=1&DeviceID=20ad85ca-becb-3bed-b3d4-30032a0f5923&Index=${index}&ZSType=7`);
                        const data = await response.json();
                        if (data.list && data.list.length > 0) {
                            sectors = sectors.concat(data.list.map(item => ({
                                code: item[0],
                                name: item[1],
                                change: parseFloat(item[2] || 0)
                            })));
                            index += 60;
                        } else {
                            break;
                        }
                    } catch (error) {
                        console.error('Error fetching all sectors:', error);
                        break;
                    }
                }
            }



 // 并行获取每个板块的个股数据
        const blockDataPromises = processedBlockData.map(async (block) => {
            try {
                const stockResponse = await fetch(`https://apphq.longhuvip.com/w1/api/index.php?Order=1&a=ZhiShuStockList_W8&st=0&c=ZhiShuRanking&PhoneOSNew=1&PlateID=${block.code}&apiv=w35&Type=6`);
                const stockResult = await stockResponse.json();
                
                if (stockResult && stockResult.list && Array.isArray(stockResult.list)) {
                    const stockData = stockResult.list
                        .map(item => {
                            if (Array.isArray(item) && item.length >= 30) {
                                return {
                                    code: item[0] || '',
                                    name: item[1] || '',
                                    price: parseFloat(item[5]) || 0,
                                    change: parseFloat(item[6]) || 0,
                                    continuous: item[23] || ''
                                };
                            }
                            return null;
                        })
                        .filter(item => item !== null);
