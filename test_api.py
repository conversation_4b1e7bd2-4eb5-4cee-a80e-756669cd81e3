#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开盘啦API接口，获取板块数据
"""

import requests
import json
import urllib.parse
import time

def test_api(plate_id):
    """
    测试API接口
    
    Args:
        plate_id (str): 板块ID
    
    Returns:
        dict: API返回的数据
    """
    base_url = "https://apphwhq.longhuvip.com/w1/api/index.php"
    
    params = {
        'DEnd': '',
        'Date': '',
        'PhoneOSNew': '2',
        'PlateID': plate_id,
        'VerSion': '********',
        'a': 'SonPlate_Info',
        'apiv': 'w41',
        'c': 'ZhiShuRanking'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(base_url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None

def display_plate_data(data, plate_id):
    """
    显示板块数据
    
    Args:
        data (dict): API返回的数据
        plate_id (str): 板块ID
    """
    if not data or data.get('errcode') != '0':
        print(f"板块 {plate_id} 数据获取失败")
        return
    
    plate_list = data.get('List', [])
    
    print(f"\n=== 板块 {plate_id} 数据 ===")
    print(f"总计: {len(plate_list)} 个子板块")
    print("-" * 50)
    print(f"{'板块代码':<10} {'板块名称':<15} {'板块强度':<10}")
    print("-" * 50)
    
    for item in plate_list:
        if len(item) >= 3:
            code = item[0]
            name = item[1]
            strength = item[2]
            print(f"{code:<10} {name:<15} {strength:<10.3f}")

def test_all_sectors_api(index=0):
    """
    测试获取所有板块数据的API接口

    Args:
        index (int): 起始索引

    Returns:
        dict: API返回的数据
    """
    base_url = "https://apphq.longhuvip.com/w1/api/index.php"

    params = {
        'Order': '1',
        'a': 'RealRankingInfo',
        'st': '60',
        'apiv': 'w26',
        'Type': '1',
        'c': 'ZhiShuRanking',
        'PhoneOSNew': '1',
        'DeviceID': '20ad85ca-becb-3bed-b3d4-30032a0f5923',
        'Index': str(index),
        'ZSType': '7'
    }

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }

    try:
        response = requests.get(base_url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None

def display_all_sectors_data(data, index):
    """
    显示所有板块数据

    Args:
        data (dict): API返回的数据
        index (int): 当前索引
    """
    if not data or data.get('errcode') != '0':
        print(f"索引 {index} 数据获取失败")
        return

    sector_list = data.get('list', [])

    print(f"\n=== 所有板块数据 (Index={index}) ===")
    print(f"总计: {len(sector_list)} 个板块")
    print(f"总板块数量: {data.get('Count', 'N/A')}")
    print("-" * 80)
    print(f"{'板块代码':<10} {'板块名称':<15} {'排名':<6} {'涨跌幅':<8} {'其他数据':<20}")
    print("-" * 80)

    for item in sector_list:
        if len(item) >= 5:
            code = item[0]
            name = item[1]
            rank = item[2] if len(item) > 2 else 'N/A'
            change_pct = item[3] if len(item) > 3 else 'N/A'
            other_data = f"{item[4]:.3f}" if len(item) > 4 and isinstance(item[4], (int, float)) else str(item[4]) if len(item) > 4 else 'N/A'

            print(f"{code:<10} {name:<15} {rank:<6} {change_pct:<8} {other_data:<20}")

def fetch_all_sectors_paginated():
    """
    分页获取所有板块数据
    """
    print("\n=== 分页获取所有板块数据 ===")
    all_sectors = []
    index = 0
    page = 1

    while True:
        print(f"\n正在获取第 {page} 页数据 (Index={index})...")
        data = test_all_sectors_api(index)

        if not data or data.get('errcode') != '0':
            print("获取数据失败，停止分页")
            break

        sector_list = data.get('list', [])
        if not sector_list:
            print("没有更多数据，停止分页")
            break

        all_sectors.extend(sector_list)
        display_all_sectors_data(data, index)

        # 检查是否还有更多数据
        total_count = data.get('Count', 0)
        if len(all_sectors) >= total_count:
            print(f"\n已获取所有数据，总计: {len(all_sectors)} 个板块")
            break

        index += 60  # 每页60条数据
        page += 1
        time.sleep(0.5)  # 避免请求过于频繁

        if page > 10:  # 限制最多获取10页，避免无限循环
            print("已达到最大页数限制")
            break

    return all_sectors

def main():
    """主函数"""
    print("开盘啦API接口测试")
    print("=" * 60)

    # 测试子板块API (之前的接口)
    print("\n=== 测试子板块API ===")

    # 测试医药板块 (801045)
    print("\n测试医药板块 (801045)...")
    data_801045 = test_api('801045')
    display_plate_data(data_801045, '801045')

    # 测试板块 801100
    print("\n测试板块 (801100)...")
    data_801100 = test_api('801100')
    display_plate_data(data_801100, '801100')

    # 比较两个板块的数据量
    if data_801045 and data_801100:
        list_801045 = data_801045.get('List', [])
        list_801100 = data_801100.get('List', [])

        print(f"\n=== 数据对比 ===")
        print(f"板块 801045 (医药板块): {len(list_801045)} 个子板块")
        print(f"板块 801100: {len(list_801100)} 个子板块")

        if len(list_801045) != len(list_801100):
            print("两个板块返回的数据量不同")
        else:
            print("两个板块返回的数据量相同")

    # 测试所有板块API (新的接口)
    print("\n\n=== 测试所有板块API ===")
    all_sectors = fetch_all_sectors_paginated()

if __name__ == "__main__":
    main()
