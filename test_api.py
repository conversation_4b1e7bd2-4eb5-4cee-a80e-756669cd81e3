#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开盘啦API接口，获取板块数据
"""

import requests
import json
import urllib.parse

def test_api(plate_id):
    """
    测试API接口
    
    Args:
        plate_id (str): 板块ID
    
    Returns:
        dict: API返回的数据
    """
    base_url = "https://apphwhq.longhuvip.com/w1/api/index.php"
    
    params = {
        'DEnd': '',
        'Date': '',
        'PhoneOSNew': '2',
        'PlateID': plate_id,
        'VerSion': '********',
        'a': 'SonPlate_Info',
        'apiv': 'w41',
        'c': 'ZhiShuRanking'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(base_url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None

def display_plate_data(data, plate_id):
    """
    显示板块数据
    
    Args:
        data (dict): API返回的数据
        plate_id (str): 板块ID
    """
    if not data or data.get('errcode') != '0':
        print(f"板块 {plate_id} 数据获取失败")
        return
    
    plate_list = data.get('List', [])
    
    print(f"\n=== 板块 {plate_id} 数据 ===")
    print(f"总计: {len(plate_list)} 个子板块")
    print("-" * 50)
    print(f"{'板块代码':<10} {'板块名称':<15} {'板块强度':<10}")
    print("-" * 50)
    
    for item in plate_list:
        if len(item) >= 3:
            code = item[0]
            name = item[1]
            strength = item[2]
            print(f"{code:<10} {name:<15} {strength:<10.3f}")

def main():
    """主函数"""
    print("开盘啦API接口测试")
    print("=" * 60)
    
    # 测试医药板块 (801045)
    print("\n测试医药板块 (801045)...")
    data_801045 = test_api('801045')
    display_plate_data(data_801045, '801045')
    
    # 测试板块 801100
    print("\n测试板块 (801100)...")
    data_801100 = test_api('801100')
    display_plate_data(data_801100, '801100')
    
    # 比较两个板块的数据量
    if data_801045 and data_801100:
        list_801045 = data_801045.get('List', [])
        list_801100 = data_801100.get('List', [])
        
        print(f"\n=== 数据对比 ===")
        print(f"板块 801045 (医药板块): {len(list_801045)} 个子板块")
        print(f"板块 801100: {len(list_801100)} 个子板块")
        
        if len(list_801045) != len(list_801100):
            print("两个板块返回的数据量不同")
        else:
            print("两个板块返回的数据量相同")

if __name__ == "__main__":
    main()
