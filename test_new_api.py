#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的开盘啦API接口 - 获取所有板块数据
"""

import requests
import json

def test_all_sectors_api(index=0):
    """
    测试获取所有板块数据的API接口
    
    Args:
        index (int): 起始索引
    
    Returns:
        dict: API返回的数据
    """
    base_url = "https://apphq.longhuvip.com/w1/api/index.php"
    
    params = {
        'Order': '1',
        'a': 'RealRankingInfo',
        'st': '60',
        'apiv': 'w26',
        'Type': '1',
        'c': 'ZhiShuRanking',
        'PhoneOSNew': '1',
        'DeviceID': '20ad85ca-becb-3bed-b3d4-30032a0f5923',
        'Index': str(index),
        'ZSType': '7'
    }
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    try:
        response = requests.get(base_url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None

def analyze_data_structure(data):
    """
    分析数据结构
    """
    if not data:
        return
        
    print("\n=== 数据结构分析 ===")
    print(f"错误码: {data.get('errcode', 'N/A')}")
    print(f"响应时间: {data.get('ttag', 'N/A')}")
    print(f"总数量: {data.get('Count', 'N/A')}")
    print(f"时间戳: {data.get('Time', 'N/A')}")
    print(f"日期: {data.get('Day', 'N/A')}")
    print(f"标题: {data.get('Title', 'N/A')}")
    
    sector_list = data.get('list', [])
    print(f"当前页板块数量: {len(sector_list)}")
    
    if sector_list:
        print(f"\n第一个板块数据结构 (共{len(sector_list[0])}个字段):")
        first_item = sector_list[0]
        for i, field in enumerate(first_item):
            print(f"  字段{i}: {field} (类型: {type(field).__name__})")

def display_top_sectors(data, limit=10):
    """
    显示排名前N的板块
    """
    if not data or data.get('errcode') != '0':
        print("数据获取失败")
        return
    
    sector_list = data.get('list', [])
    
    print(f"\n=== 排名前{limit}的板块 ===")
    print("-" * 100)
    print(f"{'排名':<6} {'板块代码':<10} {'板块名称':<15} {'涨跌幅%':<10} {'其他指标':<15}")
    print("-" * 100)
    
    for i, item in enumerate(sector_list[:limit]):
        if len(item) >= 4:
            rank = i + 1
            code = item[0]
            name = item[1]
            # 第3个字段可能是某种排名或数值
            value1 = item[2] if len(item) > 2 else 'N/A'
            # 第4个字段可能是涨跌幅
            change_pct = f"{item[3]:.3f}" if len(item) > 3 and isinstance(item[3], (int, float)) else str(item[3])
            # 第5个字段
            value2 = f"{item[4]:.3f}" if len(item) > 4 and isinstance(item[4], (int, float)) else str(item[4]) if len(item) > 4 else 'N/A'
            
            print(f"{rank:<6} {code:<10} {name:<15} {change_pct:<10} {value2:<15}")

def main():
    """主函数"""
    print("测试新的开盘啦API接口 - 获取所有板块数据")
    print("=" * 80)
    
    # 测试第一页数据
    print("\n正在获取第一页数据...")
    data = test_all_sectors_api(0)
    
    if data:
        # 分析数据结构
        analyze_data_structure(data)
        
        # 显示前10名板块
        display_top_sectors(data, 10)
        
        # 显示一些关键板块的信息
        sector_list = data.get('list', [])
        print(f"\n=== 关键板块信息 ===")
        
        # 查找医药板块
        for item in sector_list:
            if len(item) >= 2 and item[0] == '801045':
                print(f"医药板块 (801045): {item[1]}, 涨跌幅: {item[3] if len(item) > 3 else 'N/A'}")
                break
        
        # 查找生育概念板块
        for item in sector_list:
            if len(item) >= 2 and item[0] == '801100':
                print(f"生育概念板块 (801100): {item[1]}, 涨跌幅: {item[3] if len(item) > 3 else 'N/A'}")
                break
                
        # 查找人工智能板块
        for item in sector_list:
            if len(item) >= 2 and item[0] == '801085':
                print(f"人工智能板块 (801085): {item[1]}, 涨跌幅: {item[3] if len(item) > 3 else 'N/A'}")
                break
    else:
        print("API测试失败")

if __name__ == "__main__":
    main()
